<!DOCTYPE html>
<html lang="zh-CN" class="">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- SEO Meta Tags -->
    <title>探索导航 - 您的个性化上网主页</title>
    <meta name="description" content="一个简洁、高效、可定制的导航网站，汇集了全网最优质的资源。支持深色模式，无广告，响应式设计。">
    <meta name="keywords" content="导航, 网站导航, 主页, 搜索引擎, 高效工具, SEO, Web App">
    <meta name="author" content="Your App Name">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://your-domain.com/">
    <meta property="og:title" content="探索导航 - 您的个性化上网主页">
    <meta property="og:description" content="一个简洁、高效、可定制的导航网站，汇集了全网最优质的资源。">
    <meta property="og:image" content="https://your-domain.com/og-image.png">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://your-domain.com/">
    <meta property="twitter:title" content="探索导航 - 您的个性化上网主页">
    <meta property="twitter:description" content="一个简洁、高效、可定制的导航网站，汇集了全网最优质的资源。">
    <meta property="twitter:image" content="https://your-domain.com/og-image.png">

    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }

        /* 优化滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: transparent;
        }

        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        .dark ::-webkit-scrollbar-thumb {
            background: #555;
        }

        .dark ::-webkit-scrollbar-thumb:hover {
            background: #888;
        }

        .screenshot-img {
            aspect-ratio: 16 / 10;
            object-fit: cover;
            object-position: top;
        }
    </style>
</head>

<body class="bg-gray-50 dark:bg-gray-900 text-gray-800 dark:text-gray-200 transition-colors duration-300">

    <div id="app" class="min-h-screen container mx-auto px-4 py-8">

        <!-- Header -->
        <header class="flex flex-col md:flex-row justify-between items-center mb-8">
            <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4 md:mb-0">
                探索导航
            </h1>
            <div class="w-full md:w-auto flex items-center space-x-2">
                <div class="relative w-full md:w-64">
                    <input type="search" id="searchInput" placeholder="搜索网站..."
                        class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-full bg-white dark:bg-gray-800 focus:ring-2 focus:ring-blue-500 focus:outline-none">
                    <svg class="w-5 h-5 absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"
                        xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
                <button id="theme-toggle" class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700">
                    <svg id="theme-toggle-dark-icon" class="hidden w-5 h-5" fill="currentColor" viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg">
                        <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                    </svg>
                    <svg id="theme-toggle-light-icon" class="hidden w-5 h-5" fill="currentColor" viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 5.05A1 1 0 003.636 6.464l.707.707a1 1 0 001.414-1.414l-.707-.707zM3 11a1 1 0 100-2H2a1 1 0 100 2h1zM6.464 16.364l.707.707a1 1 0 01-1.414 1.414l-.707-.707a1 1 0 011.414-1.414z">
                        </path>
                    </svg>
                </button>
            </div>
        </header>

        <!-- Pinned/Starred Sites -->
        <section id="pinned-sites" class="mb-12">
            <h2 class="text-2xl font-bold mb-4 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="text-yellow-400 mr-2">
                    <polygon
                        points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                    </polygon>
                </svg>
                置顶推荐
            </h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                <!-- Pinned Site Card Example -->
                <div class="site-card" data-title="GitHub" data-tags="code,developer,social">
                    <div
                        class="bg-white dark:bg-gray-800 rounded-xl shadow-md hover:shadow-xl dark:shadow-gray-950/50 transition-all duration-300 overflow-hidden group">
                        <img class="w-full h-40 object-cover object-top"
                            src="https://api.microlink.io/?url=https%3A%2F%2Fgithub.com&screenshot=true&embed=screenshot.url"
                            alt="GitHub Screenshot"
                            onerror="this.onerror=null;this.src='https://placehold.co/600x400/333/fff?text=GitHub';">
                        <div class="p-4">
                            <div class="flex items-center mb-2">
                                <img src="https://github.com/favicon.ico" class="w-5 h-5 mr-2 rounded-sm" alt="favicon">
                                <a href="https://github.com" target="_blank"
                                    class="font-bold text-lg text-gray-900 dark:text-white stretched-link">GitHub</a>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-400 truncate">全球领先的软件开发平台。</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- All Sites -->
        <main id="all-sites">
            <!-- Category Example -->
            <div class="category-group mb-12">
                <h2 class="text-2xl font-bold mb-4">搜索引擎</h2>
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                    <!-- Site Card Example -->
                    <div class="site-card" data-title="Google" data-tags="search,engine">
                        <div
                            class="bg-white dark:bg-gray-800 rounded-xl shadow-md hover:shadow-xl dark:shadow-gray-950/50 transition-all duration-300 overflow-hidden group relative">
                            <img class="screenshot-img w-full"
                                src="https://api.microlink.io/?url=https%3A%2F%2Fgoogle.com&screenshot=true&embed=screenshot.url"
                                alt="Google Screenshot"
                                onerror="this.onerror=null;this.src='https://placehold.co/600x400/333/fff?text=Google';">
                            <div class="p-4">
                                <div class="flex items-center mb-2">
                                    <img src="https://www.google.com/favicon.ico" class="w-5 h-5 mr-2 rounded-sm"
                                        alt="favicon">
                                    <a href="https://google.com" target="_blank"
                                        class="font-bold text-lg text-gray-900 dark:text-white stretched-link">Google</a>
                                </div>
                                <p class="text-sm text-gray-600 dark:text-gray-400 truncate">全球最大的搜索引擎。</p>
                            </div>
                            <button
                                class="favorite-btn absolute top-2 right-2 p-2 bg-white/50 dark:bg-black/50 backdrop-blur-sm rounded-full text-gray-700 dark:text-gray-300 opacity-0 group-hover:opacity-100 transition-opacity">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round">
                                    <path
                                        d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z">
                                    </path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <!-- Baidu Card -->
                    <div class="site-card" data-title="Baidu 百度" data-tags="search,engine,china">
                        <div
                            class="bg-white dark:bg-gray-800 rounded-xl shadow-md hover:shadow-xl dark:shadow-gray-950/50 transition-all duration-300 overflow-hidden group relative">
                            <img class="screenshot-img w-full"
                                src="https://api.microlink.io/?url=https%3A%2F%2Fbaidu.com&screenshot=true&embed=screenshot.url"
                                alt="Baidu Screenshot"
                                onerror="this.onerror=null;this.src='https://placehold.co/600x400/333/fff?text=Baidu';">
                            <div class="p-4">
                                <div class="flex items-center mb-2">
                                    <img src="https://www.baidu.com/favicon.ico" class="w-5 h-5 mr-2 rounded-sm"
                                        alt="favicon">
                                    <a href="https://baidu.com" target="_blank"
                                        class="font-bold text-lg text-gray-900 dark:text-white stretched-link">Baidu
                                        百度</a>
                                </div>
                                <p class="text-sm text-gray-600 dark:text-gray-400 truncate">全球最大的中文搜索引擎。</p>
                            </div>
                            <button
                                class="favorite-btn absolute top-2 right-2 p-2 bg-white/50 dark:bg-black/50 backdrop-blur-sm rounded-full text-gray-700 dark:text-gray-300 opacity-0 group-hover:opacity-100 transition-opacity">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round">
                                    <path
                                        d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z">
                                    </path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <!-- 18+ Site Card (hidden by default) -->
                    <div class="site-card site-18plus hidden" data-title="18+ Site Example" data-tags="adult">
                        <div
                            class="bg-white dark:bg-gray-800 rounded-xl shadow-md hover:shadow-xl dark:shadow-gray-950/50 transition-all duration-300 overflow-hidden group relative border-2 border-red-500/50">
                            <img class="screenshot-img w-full"
                                src="https://placehold.co/600x400/a04040/fff?text=18%2B+Content"
                                alt="18+ Site Screenshot">
                            <div class="p-4">
                                <div class="flex items-center mb-2">
                                    <img src="data:image/png;base64,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-1B/gAAAABJRU5ErkJggg=="
                                        class="w-5 h-5 mr-2 rounded-sm" alt="favicon">
                                    <a href="#" target="_blank"
                                        class="font-bold text-lg text-gray-900 dark:text-white stretched-link">18+
                                        网站示例</a>
                                </div>
                                <p class="text-sm text-gray-600 dark:text-gray-400 truncate">此内容默认隐藏。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer and Controls -->
        <footer class="mt-12 text-center text-gray-500 dark:text-gray-400 text-sm">
            <div class="flex justify-center items-center space-x-4 mb-4">
                <button id="submit-site-btn"
                    class="px-4 py-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors">提交网站</button>
                <div class="flex items-center space-x-2">
                    <input type="checkbox" id="show-18plus" class="rounded">
                    <label for="show-18plus">显示 18+ 内容</label>
                </div>
            </div>
            <p>&copy; 2025 探索导航. All Rights Reserved.</p>
            <p><a href="admin.html" class="hover:underline">管理员登录</a></p>
        </footer>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // --- Theme Toggle ---
            const themeToggleBtn = document.getElementById('theme-toggle');
            const themeToggleDarkIcon = document.getElementById('theme-toggle-dark-icon');
            const themeToggleLightIcon = document.getElementById('theme-toggle-light-icon');
            const htmlEl = document.documentElement;

            // On page load or when changing themes, best to add inline in `head` to avoid FOUC
            if (localStorage.getItem('color-theme') === 'dark' || (!('color-theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                themeToggleLightIcon.classList.remove('hidden');
                htmlEl.classList.add('dark');
            } else {
                themeToggleDarkIcon.classList.remove('hidden');
                htmlEl.classList.remove('dark')
            }

            themeToggleBtn.addEventListener('click', function () {
                // toggle icons inside button
                themeToggleDarkIcon.classList.toggle('hidden');
                themeToggleLightIcon.classList.toggle('hidden');

                // if set via local storage previously
                if (localStorage.getItem('color-theme')) {
                    if (localStorage.getItem('color-theme') === 'light') {
                        htmlEl.classList.add('dark');
                        localStorage.setItem('color-theme', 'dark');
                    } else {
                        htmlEl.classList.remove('dark');
                        localStorage.setItem('color-theme', 'light');
                    }
                    // if NOT set via local storage previously
                } else {
                    if (htmlEl.classList.contains('dark')) {
                        htmlEl.classList.remove('dark');
                        localStorage.setItem('color-theme', 'light');
                    } else {
                        htmlEl.classList.add('dark');
                        localStorage.setItem('color-theme', 'dark');
                    }
                }
            });

            // --- 18+ Content Toggle ---
            const show18plusToggle = document.getElementById('show-18plus');
            const adultContent = document.querySelectorAll('.site-18plus');

            // Check local storage for user preference
            if (localStorage.getItem('show-18plus') === 'true') {
                show18plusToggle.checked = true;
                adultContent.forEach(el => el.classList.remove('hidden'));
            }

            show18plusToggle.addEventListener('change', () => {
                if (show18plusToggle.checked) {
                    if (confirm('您确认已满18周岁，并希望显示成人内容吗？')) {
                        adultContent.forEach(el => el.classList.remove('hidden'));
                        localStorage.setItem('show-18plus', 'true');
                    } else {
                        show18plusToggle.checked = false;
                    }
                } else {
                    adultContent.forEach(el => el.classList.add('hidden'));
                    localStorage.setItem('show-18plus', 'false');
                }
            });

            // --- Search Functionality ---
            const searchInput = document.getElementById('searchInput');
            const allSiteCards = document.querySelectorAll('.site-card');

            searchInput.addEventListener('input', (e) => {
                const searchTerm = e.target.value.toLowerCase();
                allSiteCards.forEach(card => {
                    const title = card.dataset.title.toLowerCase();
                    const tags = card.dataset.tags.toLowerCase();
                    if (title.includes(searchTerm) || tags.includes(searchTerm)) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });

            // --- Favorite Button ---
            document.querySelectorAll('.favorite-btn').forEach(button => {
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    const svg = button.querySelector('svg');
                    if (svg.getAttribute('fill') === 'currentColor') {
                        svg.setAttribute('fill', 'none');
                        svg.classList.remove('text-red-500');
                    } else {
                        svg.setAttribute('fill', 'currentColor');
                        svg.classList.add('text-red-500');
                    }
                    // In a real app, you would save this state to localStorage
                });
            });
        });
    </script>
</body>

</html>