# 管理员认证服务使用说明

## 🎯 概述

我已经成功将 `GET /api/admin/auth` 的认证逻辑抽象为通用的 `AdminAuthService` 类和便捷函数，现在可以在整个应用中复用这些认证方法。

## 📁 核心文件

### `src/lib/server/auth.ts`
包含完整的管理员认证服务类和便捷函数。

## 🔧 主要功能

### 1. AdminAuthService 类

#### 静态方法列表：

- **`validateCredentials(credentials)`** - 验证登录凭据
- **`createSession(username)`** - 创建会话对象
- **`setSessionCookie(cookies, session)`** - 设置会话 Cookie
- **`getSession(cookies)`** - 获取当前会话
- **`isAuthenticated(cookies)`** - 检查是否已认证
- **`getAuthenticatedUsername(cookies)`** - 获取当前用户名
- **`clearSession(cookies)`** - 清除会话
- **`verifyApiAccess(cookies)`** - 验证 API 访问权限
- **`refreshSession(cookies)`** - 刷新会话过期时间
- **`getSessionStatus(cookies)`** - 获取详细会话状态

### 2. 便捷函数

- **`checkAdminAuth(cookies)`** - 检查管理员登录状态
- **`requireAdminAuth(cookies)`** - 要求管理员权限（抛出异常）
- **`verifyAdminApiAccess(cookies)`** - 验证 API 访问权限
- **`requireAdminPage(cookies, redirectTo)`** - 页面级认证中间件
- **`checkAdminPage(cookies)`** - 可选的管理员功能检查

## 🚀 使用示例

### 1. API 端点中使用

#### 验证 API 访问权限
```typescript
// src/routes/api/admin/sites/+server.ts
import { verifyAdminApiAccess } from '$lib/server/auth.js';

export const GET: RequestHandler = async ({ cookies }) => {
  // 验证管理员权限
  const authResult = verifyAdminApiAccess(cookies);
  
  if (!authResult.isAuthorized) {
    return json({
      success: false,
      error: 'UNAUTHORIZED',
      message: authResult.error || '需要管理员权限'
    }, { status: 401 });
  }

  // 继续处理已认证的请求
  // ...
};
```

#### 使用 AdminAuthService 类
```typescript
import { AdminAuthService } from '$lib/server/auth.js';

export const POST: RequestHandler = async ({ request, cookies }) => {
  // 验证凭据
  const credentials = await request.json();
  const validation = AdminAuthService.validateCredentials(credentials);
  
  if (!validation.isValid) {
    return json({ error: validation.error }, { status: 401 });
  }

  // 创建会话
  const session = AdminAuthService.createSession(credentials.username);
  AdminAuthService.setSessionCookie(cookies, session);
  
  return json({ success: true });
};
```

### 2. 页面服务端使用

#### 要求管理员权限的页面
```typescript
// src/routes/admin/dashboard/+page.server.ts
import { requireAdminPage } from '$lib/server/auth.js';

export const load: PageServerLoad = async ({ cookies }) => {
  // 要求管理员权限，未认证会自动重定向到 /admin
  const adminSession = requireAdminPage(cookies);
  
  return {
    admin: {
      username: adminSession.username,
      loginTime: new Date()
    }
  };
};
```

#### 可选管理员功能的页面
```typescript
// src/routes/+page.server.ts
import { checkAdminPage } from '$lib/server/auth.js';

export const load: PageServerLoad = async ({ cookies }) => {
  // 检查管理员权限但不强制重定向
  const { isAdmin, session } = checkAdminPage(cookies);
  
  return {
    isAdmin,
    adminUsername: session?.username
  };
};
```

### 3. 检查认证状态

#### 简单检查
```typescript
import { AdminAuthService } from '$lib/server/auth.js';

// 检查是否已认证
const isAuth = AdminAuthService.isAuthenticated(cookies);

// 获取用户名
const username = AdminAuthService.getAuthenticatedUsername(cookies);
```

#### 详细状态检查
```typescript
import { AdminAuthService } from '$lib/server/auth.js';

const status = AdminAuthService.getSessionStatus(cookies);
console.log({
  isAuthenticated: status.isAuthenticated,
  username: status.username,
  expiresAt: status.expiresAt,
  timeRemaining: status.timeRemaining // 毫秒
});
```

### 4. 会话管理

#### 刷新会话
```typescript
import { AdminAuthService } from '$lib/server/auth.js';

// 刷新会话过期时间
const refreshed = AdminAuthService.refreshSession(cookies);
if (refreshed) {
  console.log('会话已刷新');
}
```

#### 清除会话
```typescript
import { AdminAuthService } from '$lib/server/auth.js';

// 登出时清除会话
AdminAuthService.clearSession(cookies);
```

## 📊 已更新的文件

### 1. API 端点
- **`src/routes/api/admin/auth/+server.ts`** - 使用新的认证服务重构
- **`src/routes/api/admin/sites/+server.ts`** - 示例管理员 API（新建）

## 🔐 安全特性

### 1. 会话管理
- **HttpOnly Cookies**: 防止 XSS 攻击
- **Secure Cookies**: 生产环境启用 HTTPS
- **SameSite**: 防止 CSRF 攻击
- **自动过期**: 24小时会话超时

### 2. 权限验证
- **统一验证**: 所有 API 使用相同的验证逻辑
- **自动重定向**: 页面级认证失败自动跳转
- **错误处理**: 完善的错误信息和状态码

### 3. 环境配置
- **环境变量**: 凭据存储在 `.env` 文件
- **配置验证**: 启动时检查必要配置
- **开发模式**: 开发环境特殊处理

## 🎨 使用建议

### 1. API 开发
```typescript
// 推荐：使用便捷函数
const authResult = verifyAdminApiAccess(cookies);
if (!authResult.isAuthorized) {
  return json({ error: authResult.error }, { status: 401 });
}

// 或者：使用类方法获得更多控制
const session = AdminAuthService.getSession(cookies);
if (!session?.isAuthenticated) {
  return json({ error: '未认证' }, { status: 401 });
}
```

### 2. 页面开发
```typescript
// 强制认证页面
const adminSession = requireAdminPage(cookies);

// 可选认证页面
const { isAdmin, session } = checkAdminPage(cookies);
```

### 3. 错误处理
```typescript
try {
  const adminSession = requireAdminAuth(cookies);
  // 处理已认证的请求
} catch (error) {
  // 处理认证失败
  return json({ error: '认证失败' }, { status: 401 });
}
```

## 🔄 扩展功能

### 1. 多角色支持
可以扩展 `AdminSession` 接口添加角色字段：
```typescript
interface AdminSession {
  isAuthenticated: boolean;
  username?: string;
  role?: 'admin' | 'moderator' | 'editor';
  permissions?: string[];
  expiresAt?: number;
}
```

### 2. 审计日志
可以在认证服务中添加日志记录：
```typescript
static logAuthEvent(event: string, username?: string) {
  console.log(`[AUTH] ${event} - ${username || 'anonymous'} - ${new Date().toISOString()}`);
}
```

### 3. 速率限制
可以添加登录尝试限制：
```typescript
static checkRateLimit(ip: string): boolean {
  // 实现 IP 基础的速率限制
  return true;
}
```

这个认证服务现在提供了完整的、可复用的管理员认证功能，可以在整个应用中安全地使用。
