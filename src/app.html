<!doctype html>
<html lang="zh-CN" class="">

<head>
	<meta charset="utf-8" />
	<link rel="icon" href="%sveltekit.assets%/favicon.png" />
	<meta name="viewport" content="width=device-width, initial-scale=1" />

	<!-- SEO Meta Tags -->
	<meta name="description" content="一个简洁、高效、可定制的导航网站，汇集了全网最优质的资源。支持深色模式，无广告，响应式设计。">
	<meta name="keywords" content="导航, 网站导航, 主页, 搜索引擎, 高效工具, SEO, Web App">
	<meta name="author" content="探索导航">

	<!-- Open Graph / Facebook -->
	<meta property="og:type" content="website">
	<meta property="og:title" content="探索导航 - 您的个性化上网主页">
	<meta property="og:description" content="一个简洁、高效、可定制的导航网站，汇集了全网最优质的资源。">

	<!-- Twitter -->
	<meta property="twitter:card" content="summary_large_image">
	<meta property="twitter:title" content="探索导航 - 您的个性化上网主页">
	<meta property="twitter:description" content="一个简洁、高效、可定制的导航网站，汇集了全网最优质的资源。">

	%sveltekit.head%
</head>

<body data-sveltekit-preload-data="hover"
	class="bg-gray-50 dark:bg-gray-900 text-gray-800 dark:text-gray-200 transition-colors duration-300">
	<div style="display: contents">%sveltekit.body%</div>
</body>

</html>
<script>
	// 检查本地存储或系统偏好
	if (typeof window !== 'undefined') {
		const stored = localStorage.getItem('color-theme');

		let shouldBeDark;

		// 根据存储的主题模式决定是否使用深色主题
		switch (stored) {
			case 'dark':
				shouldBeDark = true;
				break;
			case 'light':
				shouldBeDark = false;
				break;
			case 'system':
			case null:
			case undefined:
			default:
				shouldBeDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
				break;
		}

		if (shouldBeDark) {
			document.documentElement.classList.add('dark');
		} else {
			document.documentElement.classList.remove('dark');
		}
	}
</script>