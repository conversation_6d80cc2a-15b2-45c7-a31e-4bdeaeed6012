导航 Web App 产品需求文档 (PRD)
版本: 1.1
日期: 2025-07-06

1. 项目概述
1.1. 项目背景
随着互联网信息的爆炸式增长，用户需要一个高效、个性化且安全的门户来访问他们常用和感兴趣的网站。当前市场上的导航页功能单一，广告繁多，且缺乏个性化定制。本项目旨在开发一款现代、美观、无广告、SEO 友好且高度可定制的导航 Web App，提升用户的网络浏览体验。

1.2. 项目目标
核心目标: 为用户提供一个快速、干净、个性化的网站导航入口。

用户目标:

普通用户：能够轻松收藏和访问常用网站，发现优质内容，并享受无干扰的浏览体验。

管理员：能够高效地管理和维护导航内容，保证网站数据的质量和安全性。

商业/技术目标:

实现对主流搜索引擎（Google, Bing, Baidu, Yahoo, Yandex, DuckDuckGo 等）的 SEO 友好，获取自然流量。

构建一个响应式布局，适配桌面、平板和移动设备。

支持用户个性化设置，如深色/浅色模式、收藏、置顶等。

建立一个高效、自动化的后台管理流程，降低维护成本。

2. 功能需求
2.1. 用户端功能
功能模块

优先级

功能描述

网站展示

高

- 以卡片形式网格布局展示网站列表。<br>- 每个卡片包含网站截图、Favicon、标题和简短描述。<br>- 网站截图使用第三方服务动态生成：https://api.microlink.io/?url={site_url}&screenshot=true&embed=screenshot.url。

响应式布局

高

- 页面布局能自动适应不同尺寸的屏幕（手机、平板、桌面电脑）。

深色/浅色模式

高

- 默认跟随操作系统的颜色模式。<br>- 提供手动切换按钮，允许用户覆盖系统设置。

SEO 优化

高

- 针对主流搜索引擎进行优化，包括但不限于：<br>  - 动态生成 sitemap.xml 和 robots.txt。<br>  - 每个网站详情页（如果未来扩展）有独立的 title, description meta 标签。<br>  - 首页支持 Open Graph (OG) 协议，便于社交媒体分享。

网站收藏

中

- 用户可以将喜欢的网站收藏到本地。<br>- 收藏状态保存在浏览器的 localStorage 中。<br>- 收藏的网站在卡片上有明显标识（如心形图标）。

网站置顶

高

- 管理员可以“标星”或“置顶”特定网站。<br>- 被置顶的网站会显示在页面的顶部专属区域，更加突出。

内容分级

高

- 默认情况下，标记为 18+ 的网站将被隐藏。<br>- 提供一个明确的开关/选项，允许成年用户选择显示 18+ 内容，该选择需要用户确认并记录在 localStorage 中。

用户提交网站

中

- 提供一个入口，允许用户提交新的网站 URL。<br>- 用户提交的网站不会立即显示，而是进入后台待审核队列 (todo.csv)。<br>- 提交时，后端自动记录提交者的 IP 地址、浏览器语言、操作系统和浏览器类型。

搜索功能

高

- 提供搜索框，可以根据网站的标题、描述、标签实时筛选网站。

2.2. 管理员端功能
功能模块

优先级

功能描述

管理员认证

高

- 管理员通过一个独立的后台页面登录。<br>- 用户名和密码通过环境变量（.env 文件）在后端进行配置，而非硬编码。

添加新网站

高

- 管理员在后台提供一个网站 URL。<br>- 后端服务自动执行以下操作：<br>  1. 获取信息: 抓取网站的 title, description, favicon。<br>  2. 自动分类: 通过网站内容或元数据，尝试自动归类（如：科技、新闻、购物）。<br>  3. 自动分级: 基于关键词或第三方服务判断是否为 18+ 内容。<br>  4. 技术检测: 自动检测网站是否支持 PWA 和 HTTPS。<br>  5. 手动补充: 管理员可以手动添加或修改标签（Tags）。<br>- 所有信息处理完毕后，生成一条新数据并写入 sites.txt。

审核用户提交

中

- 后台提供一个审核列表，展示所有从 todo.csv 读取的待审核网站。<br>- 列表会显示网站 URL 及提交者的附加信息（IP, 语言, OS, 浏览器）供管理员参考。<br>- 管理员可以对每个提交进行“批准”或“拒绝”操作。<br>- “批准”后，网站将走“添加新网站”的流程，并存入 sites.txt。<br>- “拒绝”后，该条记录从 todo.csv 中移除。

数据管理

高

- 管理员可以编辑或删除 sites.txt 中的任何一条网站数据。<br>- 当发现某个网站无法访问时，管理员可以将其从 sites.txt 移动到 404.txt 中，作为存档。

3. 数据源与数据结构
3.1. 数据源
存储位置: 一个公开的 GitHub 仓库。

授权方式: 后端服务使用一个具有 repo 范围写权限的 GitHub Fine-grained Personal Access Token 来操作仓库文件。

数据文件:

sites.txt: 存储所有有效和显示的网站数据。

404.txt: 存储已失效或被移除的网站数据，用于备份和记录。

todo.csv: (新增) 存储用户提交、待管理员审核的网站数据。

3.2. 数据结构
sites.txt / 404.txt
每个网站存储为连续的 14 行文本，结构如下：

line 0: 标题 (e.g., Google)
line 1: 网址 (e.g., https://www.google.com)
line 2: Favicon URL (e.g., https://www.google.com/favicon.ico)
line 3: 网站描述 (e.g., 全球最大的搜索引擎)
line 4: 分类 (e.g., 搜索引擎)
line 5: 标签 (e.g., search,tech,google)
line 6: 年龄分级 (e.g., SFW 或 18+)
line 7: 语言 (e.g., zh-CN, en-US)
line 8: 标星 (e.g., 1 代表标星, 0 代表不标星)
line 9: 是否支持 PWA (e.g., true 或 false)
line 10: 是否支持 HTTPS (e.g., true 或 false)
line 11: 推荐语
line 12: 上架时间 (e.g., 2025-07-06T12:34:56Z)
line 13: og image

注意: 两条网站数据之间用一个空行分隔。

todo.csv (新增)
采用 CSV 格式，包含表头，数据结构如下：

url,ip_address,language,os,browser,submitted_at,status
"https://example.com","***********","zh-CN,zh;q=0.9","Windows","Chrome",2025-07-06T12:34:56Z,pending
"https://another-site.net","************","en-US,en;q=0.9","macOS","Safari",2025-07-06T12:35:01Z,approved

url: 用户提交的网址。

ip_address: 提交者的 IP 地址。

language: 提交者浏览器的 Accept-Language 请求头（只取第一个主要语言即可，如 zh-CN）。

os: 提交者的操作系统。

browser: 提交者的浏览器。

4. 非功能性需求
类别

需求描述

性能

- 页面首次加载时间（FCP）应小于 2 秒。<br>- 交互操作（如搜索、切换模式）应无明显延迟。

安全

- 管理员认证凭据必须安全存储，不能暴露在前端。<br>- 所有用户输入（如提交的 URL）在后端处理前必须进行清理和验证，防止 XSS 等攻击。<br>- 与 GitHub API 的通信必须通过 HTTPS。

可用性

- 界面设计直观，用户无需学习即可使用。<br>- 所有可点击元素在移动设备上有足够大的触控区域。

兼容性

- 支持最新版本的 Chrome, Firefox, Safari, Edge 浏览器。

5. 技术栈设想
前端: sveltekit, Tailwind CSS v4 (用于快速构建 UI) + ts。管理员页面由前端渲染, 其他页面由服务端沉浸.

后端: sveltekit。

第三方服务:

Microlink API: 用于生成网站截图。

GitHub API: 用于读写数据源。
GitHub RAW API: 用于只读操作.